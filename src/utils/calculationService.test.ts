import { describe, it, expect, beforeEach } from "vitest";
import {
  CalculationService,
  calculateCosts,
  calculateYearlyBreakdowns,
} from "./calculationService";
import { ExpenseCategory } from "@/types/costCalculation";

describe("CalculationService", () => {
  const mockExpenses: ExpenseCategory[] = [
    {
      id: "1",
      name: "Food",
      iconName: "Utensils",
      monthlyAmount: 10000,
      frequency: "monthly",
    },
    {
      id: "2",
      name: "Vacation",
      iconName: "Plane",
      monthlyAmount: 50000,
      frequency: "yearly",
    },
    {
      id: "3",
      name: "Laptop",
      iconName: "Laptop",
      monthlyAmount: 80000,
      frequency: "one-time",
      yearsInterval: 4,
    },
  ];

  const defaultParams = {
    retirementAge: 60,
    retirementDuration: 20,
    currentAge: 30,
    inflationRate: 6,
    annualReturn: 12,
  };

  describe("Constructor", () => {
    it("should initialize with provided parameters", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      expect(service).toBeInstanceOf(CalculationService);
    });

    it("should use default inflation rate when not provided", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge
      );

      const result = service.calculateAll();
      expect(result).toBeDefined();
    });
  });

  describe("calculateAll", () => {
    let service: CalculationService;

    beforeEach(() => {
      service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );
    });

    it("should return all required result properties", () => {
      const result = service.calculateAll();

      expect(result).toHaveProperty("yearlyBreakdowns");
      expect(result).toHaveProperty("aggregatedResults");
      expect(result).toHaveProperty("costResults");
      expect(result).toHaveProperty("oneTimeOccurrences");
    });

    it("should calculate yearly breakdowns for each retirement year", () => {
      const result = service.calculateAll();

      expect(result.yearlyBreakdowns).toHaveLength(
        defaultParams.retirementDuration
      );

      result.yearlyBreakdowns.forEach((breakdown, index) => {
        expect(breakdown.year).toBe(index + 1);
        expect(breakdown.age).toBe(defaultParams.retirementAge + index);
        expect(breakdown.monthlyExpenses).toBeGreaterThan(0);
        expect(breakdown.totalYearlyExpenses).toBeGreaterThan(0);
      });
    });

    it("should calculate aggregated results correctly", () => {
      const result = service.calculateAll();
      const { aggregatedResults } = result;

      expect(aggregatedResults.totalRetirementCost).toBeGreaterThan(0);
      expect(aggregatedResults.breakdown.monthly).toBeGreaterThan(0);
      // Note: yearly breakdown might be 0 if no yearly expenses in retirement years
      expect(aggregatedResults.breakdown.yearly).toBeGreaterThanOrEqual(0);
      expect(aggregatedResults.breakdown.oneTime).toBeGreaterThan(0);
      expect(aggregatedResults.averageAnnualCost).toBeGreaterThan(0);
      expect(aggregatedResults.currentAnnualCost).toBeGreaterThan(0);
    });

    it("should calculate cost results with proper structure", () => {
      const result = service.calculateAll();
      const { costResults } = result;

      expect(costResults.currentMonthlyCost).toBe(10000); // Monthly food expense
      expect(costResults.currentAnnualCost).toBeGreaterThan(0);
      expect(costResults.adjustedMonthlyCost).toBeGreaterThan(
        costResults.currentMonthlyCost
      );
      expect(costResults.totalRetirementCost).toBeGreaterThan(0);
      expect(costResults.breakdown).toHaveProperty("monthly");
      expect(costResults.breakdown).toHaveProperty("yearly");
      expect(costResults.breakdown).toHaveProperty("oneTime");
      expect(costResults.detailedBreakdown).toBeDefined();
    });

    it("should handle one-time expenses correctly", () => {
      const result = service.calculateAll();

      // Should have one-time occurrences for laptop every 4 years
      expect(result.oneTimeOccurrences.length).toBeGreaterThan(0);

      const laptopOccurrences = result.oneTimeOccurrences.filter(
        (occ) => occ.expenseName === "Laptop"
      );

      // In 20 years with 4-year intervals, should have 5 occurrences (years 4, 8, 12, 16, 20)
      expect(laptopOccurrences).toHaveLength(5);
    });

    it("should apply inflation correctly over time", () => {
      const result = service.calculateAll();
      const { yearlyBreakdowns } = result;

      // First year should have lower costs than last year due to inflation
      const firstYear = yearlyBreakdowns[0];
      const lastYear = yearlyBreakdowns[yearlyBreakdowns.length - 1];

      expect(lastYear.monthlyExpenses).toBeGreaterThan(
        firstYear.monthlyExpenses
      );
      expect(lastYear.yearlyExpenses).toBeGreaterThan(firstYear.yearlyExpenses);
    });

    it("should calculate detailed breakdown correctly", () => {
      const result = service.calculateAll();
      const { detailedBreakdown } = result.costResults;

      expect(detailedBreakdown.currentYearlyCost).toBe(50000); // Vacation expense
      expect(detailedBreakdown.adjustedYearlyCost).toBeGreaterThan(
        detailedBreakdown.currentYearlyCost
      );
      expect(detailedBreakdown.yearlyExpensesByCategory).toHaveLength(1);
      expect(detailedBreakdown.oneTimeExpensesByCategory).toHaveLength(1);

      const yearlyExpense = detailedBreakdown.yearlyExpensesByCategory[0];
      expect(yearlyExpense.name).toBe("Vacation");
      expect(yearlyExpense.frequency).toBe("yearly");

      const oneTimeExpense = detailedBreakdown.oneTimeExpensesByCategory[0];
      expect(oneTimeExpense.name).toBe("Laptop");
      expect(oneTimeExpense.frequency).toBe("one-time");
      expect(oneTimeExpense.yearsInterval).toBe(4);
      expect(oneTimeExpense.totalOccurrences).toBe(5);
    });

    it("should calculate correct numerical values with known inputs", () => {
      // Create a service with simple, predictable values for validation
      const simpleExpenses: ExpenseCategory[] = [
        {
          id: "1",
          name: "Simple Monthly",
          iconName: "Home",
          monthlyAmount: 1000, // ₹1,000 per month
          frequency: "monthly",
        },
        {
          id: "2",
          name: "Simple Yearly",
          iconName: "Calendar",
          monthlyAmount: 12000, // ₹12,000 per year
          frequency: "yearly",
        },
        {
          id: "3",
          name: "Simple One-time",
          iconName: "Laptop",
          monthlyAmount: 10000, // ₹10,000 every 5 years
          frequency: "one-time",
          yearsInterval: 5,
        },
      ];

      const simpleService = new CalculationService(
        simpleExpenses,
        35, // retirement age
        10, // retirement duration (10 years)
        30, // current age
        0, // 0% inflation for easier calculation
        10 // 10% annual return
      );

      const result = simpleService.calculateAll();

      // With 0% inflation, costs should remain constant
      // Monthly: ₹1,000 * 12 months * 10 years = ₹120,000
      expect(result.aggregatedResults.breakdown.monthly).toBe(120000);

      // Yearly expenses: ₹12,000 per year * 10 years = ₹120,000
      expect(result.aggregatedResults.breakdown.yearly).toBe(120000);

      // One-time: ₹10,000 * 2 occurrences (years 5 and 10) = ₹20,000
      expect(result.aggregatedResults.breakdown.oneTime).toBe(20000);

      // Total: ₹120,000 + ₹120,000 + ₹20,000 = ₹260,000
      expect(result.aggregatedResults.totalRetirementCost).toBe(260000);

      // Current costs (before retirement)
      expect(result.costResults.currentMonthlyCost).toBe(1000);

      // With 0% inflation, adjusted costs should equal current costs
      expect(result.costResults.adjustedMonthlyCost).toBe(1000);

      // Verify one-time occurrences (they occur in retirement years 5 and 10)
      expect(result.oneTimeOccurrences).toHaveLength(2);
      expect(result.oneTimeOccurrences[0].age).toBe(39); // 35 + 4 (year 5 of retirement)
      expect(result.oneTimeOccurrences[1].age).toBe(44); // 35 + 9 (year 10 of retirement)
      expect(result.oneTimeOccurrences[0].amount).toBe(10000);
      expect(result.oneTimeOccurrences[1].amount).toBe(10000);

      // Verify yearly breakdown structure shows yearly expenses per year
      const firstYear = result.yearlyBreakdowns[0];
      expect(firstYear.yearlyExpenses).toBe(12000); // Should have yearly expense in each year
      expect(firstYear.monthlyExpenses).toBe(12000); // Monthly: 1000 * 12

      // Verify that yearly expenses appear in the detailed breakdown
      expect(result.costResults.detailedBreakdown.currentYearlyCost).toBe(
        12000
      );
      expect(
        result.costResults.detailedBreakdown.yearlyExpensesByCategory
      ).toHaveLength(1);
      expect(
        result.costResults.detailedBreakdown.yearlyExpensesByCategory[0].name
      ).toBe("Simple Yearly");
    });

    it("should calculate correct inflation adjustments", () => {
      // Test with known inflation rate
      const testExpenses: ExpenseCategory[] = [
        {
          id: "1",
          name: "Test Monthly",
          iconName: "Home",
          monthlyAmount: 1000,
          frequency: "monthly",
        },
      ];

      const inflationService = new CalculationService(
        testExpenses,
        32, // retirement age (2 years from now)
        5, // retirement duration
        30, // current age
        10, // 10% inflation
        12 // annual return
      );

      const result = inflationService.calculateAll();

      // After 2 years with 10% inflation: 1000 * (1.10)^2 = 1210
      const expectedAdjustedMonthlyCost = 1000 * Math.pow(1.1, 2);
      expect(result.costResults.adjustedMonthlyCost).toBeCloseTo(
        expectedAdjustedMonthlyCost,
        2
      );

      // Current cost should remain unchanged
      expect(result.costResults.currentMonthlyCost).toBe(1000);

      // Verify inflation is applied progressively in yearly breakdowns
      const firstYear = result.yearlyBreakdowns[0];
      const lastYear = result.yearlyBreakdowns[4]; // 5th year

      // First year should have the base adjusted amount
      expect(firstYear.monthlyExpenses).toBeCloseTo(
        expectedAdjustedMonthlyCost * 12,
        2
      );

      // Last year should have additional inflation applied
      const lastYearExpected =
        expectedAdjustedMonthlyCost * Math.pow(1.1, 4) * 12;
      expect(lastYear.monthlyExpenses).toBeCloseTo(lastYearExpected, 2);
    });
  });

  describe("generateEnhancedProjections", () => {
    let service: CalculationService;

    beforeEach(() => {
      service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );
    });

    it("should generate enhanced projections with retirement data", () => {
      const mockProjectionData = {
        yearlyProjections: [
          {
            age: 30,
            year: 2024,
            portfolioValue: 500000,
            realValue: 500000,
            cumulativeContributions: 100000,
          },
        ],
        finalValue: 5000000,
        totalContributions: 1000000,
        monthlyRetirementIncome: 50000,
        realMonthlyIncome: 30000,
        currentAge: 30,
      };

      const oneTimeOccurrences = [
        {
          age: 62,
          year: 2026,
          expenseName: "Laptop",
          amount: 100000,
          totalAmount: 100000,
        },
      ];

      const adjustedAnnualCost = 200000; // Reduced to make test more sustainable

      const result = service.generateEnhancedProjections(
        mockProjectionData,
        oneTimeOccurrences,
        adjustedAnnualCost
      );

      expect(result).toHaveLength(defaultParams.retirementDuration + 1);

      // Check retirement years have proper structure
      const retirementYears = result.filter((point) => point.isRetirement);
      expect(retirementYears).toHaveLength(defaultParams.retirementDuration);

      retirementYears.forEach((point, index) => {
        // annualCost should now include inflation during retirement
        // For the first year (index 0), it should be the base adjustedAnnualCost
        // For subsequent years, it should be higher due to inflation
        if (index === 0) {
          expect(point.annualCost).toBe(adjustedAnnualCost);
        } else {
          expect(point.annualCost).toBeGreaterThan(adjustedAnnualCost);
        }
        expect(point.consolidatedCosts).toBeGreaterThanOrEqual(
          point.annualCost!
        );
        // Only check for positive income in early retirement years
        // as portfolio may get depleted in later years with inflation
        if (index < 10) {
          expect(point.incomeFromInterest).toBeGreaterThan(0);
        } else {
          expect(point.incomeFromInterest).toBeGreaterThanOrEqual(0);
        }
        expect(point.netPosition).toBeDefined();
      });
    });

    it("should handle one-time costs in projections", () => {
      const mockProjectionData = {
        yearlyProjections: [
          {
            age: 30,
            year: 2024,
            portfolioValue: 500000,
            realValue: 500000,
            cumulativeContributions: 100000,
          },
        ],
        finalValue: 5000000,
        totalContributions: 1000000,
        monthlyRetirementIncome: 50000,
        realMonthlyIncome: 30000,
        currentAge: 30,
      };

      const oneTimeOccurrences = [
        {
          age: 61, // First year of retirement
          year: 2025,
          expenseName: "Laptop",
          amount: 100000,
          totalAmount: 100000,
        },
      ];

      const adjustedAnnualCost = 500000;

      const result = service.generateEnhancedProjections(
        mockProjectionData,
        oneTimeOccurrences,
        adjustedAnnualCost
      );

      const firstRetirementYear = result.find((point) => point.age === 61);
      expect(firstRetirementYear?.oneTimeCosts).toBe(100000);
      expect(firstRetirementYear?.consolidatedCosts).toBe(
        adjustedAnnualCost + 100000
      );
    });

    it("should validate consolidatedCosts increases based on inflation", () => {
      // Create test expenses with predictable amounts
      const testExpenses: ExpenseCategory[] = [
        {
          id: "1",
          name: "Monthly Living",
          iconName: "Home",
          monthlyAmount: 10000, // ₹10,000 per month
          frequency: "monthly",
        },
        {
          id: "2",
          name: "Annual Vacation",
          iconName: "Plane",
          monthlyAmount: 60000, // ₹60,000 per year
          frequency: "yearly",
        },
      ];

      const testService = new CalculationService(
        testExpenses,
        35, // retirement age
        10, // retirement duration (10 years)
        30, // current age
        8, // 8% inflation rate
        12 // annual return
      );

      const mockProjectionData = {
        yearlyProjections: [
          {
            age: 30,
            year: 2024,
            portfolioValue: 5000000,
            realValue: 5000000,
            cumulativeContributions: 1000000,
          },
        ],
        finalValue: 5000000,
        totalContributions: 1000000,
        monthlyRetirementIncome: 50000,
        realMonthlyIncome: 30000,
        currentAge: 30,
      };

      // Calculate the base annual cost (monthly + yearly expenses adjusted for inflation to retirement)
      // Monthly: ₹10,000 * 12 = ₹120,000 per year
      // Yearly: ₹60,000 per year
      // Total current annual cost: ₹180,000
      // After 5 years of 8% inflation: ₹180,000 * (1.08)^5 ≈ ₹264,665
      const yearsToRetirement = 5;
      const currentAnnualCost = 10000 * 12 + 60000; // ₹180,000
      const adjustedAnnualCost =
        currentAnnualCost * Math.pow(1.08, yearsToRetirement);

      const result = testService.generateEnhancedProjections(
        mockProjectionData,
        [], // No one-time occurrences for this test
        adjustedAnnualCost
      );

      // Filter only retirement years
      const retirementYears = result.filter((point) => point.isRetirement);
      expect(retirementYears).toHaveLength(10);

      // Verify consolidatedCosts increases each year due to inflation during retirement
      for (let i = 1; i < retirementYears.length; i++) {
        const currentYear = retirementYears[i];
        const previousYear = retirementYears[i - 1];

        // consolidatedCosts should increase each year due to inflation
        expect(currentYear.consolidatedCosts).toBeGreaterThan(
          previousYear.consolidatedCosts!
        );

        // Calculate expected inflation increase (should be approximately 8% per year)
        const expectedIncrease = previousYear.consolidatedCosts! * 0.08;
        const actualIncrease =
          currentYear.consolidatedCosts! - previousYear.consolidatedCosts!;

        // Allow for some tolerance due to rounding (within ₹1,000)
        expect(actualIncrease).toBeCloseTo(expectedIncrease, -3);
      }

      // Verify the inflation progression over multiple years
      const firstYear = retirementYears[0];
      const fifthYear = retirementYears[4]; // 5th year of retirement
      const lastYear = retirementYears[9]; // 10th year of retirement

      // After 4 years of 8% inflation during retirement: cost should be approximately 1.08^4 ≈ 1.36 times higher
      const expectedFifthYearMultiplier = Math.pow(1.08, 4);
      expect(fifthYear.consolidatedCosts).toBeCloseTo(
        firstYear.consolidatedCosts! * expectedFifthYearMultiplier,
        -3 // Within ₹1,000
      );

      // After 9 years of 8% inflation during retirement: cost should be approximately 1.08^9 ≈ 2.00 times higher
      const expectedLastYearMultiplier = Math.pow(1.08, 9);
      expect(lastYear.consolidatedCosts).toBeCloseTo(
        firstYear.consolidatedCosts! * expectedLastYearMultiplier,
        -3 // Within ₹1,000
      );

      // Verify that consolidatedCosts represents the total yearly cost
      // In years without one-time costs, consolidatedCosts should equal annualCost
      retirementYears.forEach((year) => {
        if (!year.oneTimeCosts) {
          expect(year.consolidatedCosts).toBe(year.annualCost);
        } else {
          expect(year.consolidatedCosts).toBe(
            year.annualCost! + year.oneTimeCosts
          );
        }
      });

      // Verify that the first year's consolidatedCosts matches our expected adjusted annual cost
      expect(firstYear.consolidatedCosts).toBeCloseTo(adjustedAnnualCost, -3);
    });
  });

  describe("Edge Cases", () => {
    it("should handle empty expenses array", () => {
      const service = new CalculationService(
        [],
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      expect(result.aggregatedResults.totalRetirementCost).toBe(0);
      expect(result.costResults.currentMonthlyCost).toBe(0);
      expect(result.oneTimeOccurrences).toHaveLength(0);
    });

    it("should handle zero inflation rate", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        0, // Zero inflation
        defaultParams.annualReturn
      );

      const result = service.calculateAll();
      const { yearlyBreakdowns } = result;

      // With zero inflation, all years should have same monthly expenses
      const firstYear = yearlyBreakdowns[0];
      const lastYear = yearlyBreakdowns[yearlyBreakdowns.length - 1];

      expect(firstYear.monthlyExpenses).toBeCloseTo(
        lastYear.monthlyExpenses,
        2
      );
    });

    it("should handle one-time expenses with large intervals", () => {
      const expensesWithLargeInterval: ExpenseCategory[] = [
        {
          id: "1",
          name: "House Renovation",
          iconName: "Home",
          monthlyAmount: 1000000,
          frequency: "one-time",
          yearsInterval: 25, // Longer than retirement duration
        },
      ];

      const service = new CalculationService(
        expensesWithLargeInterval,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      // Should have no one-time occurrences since interval > retirement duration
      expect(result.oneTimeOccurrences).toHaveLength(0);
      expect(result.aggregatedResults.breakdown.oneTime).toBe(0);
    });

    it("should handle one-time expenses without yearsInterval", () => {
      const expensesWithoutInterval: ExpenseCategory[] = [
        {
          id: "1",
          name: "Invalid One-time",
          iconName: "Home",
          monthlyAmount: 1000000,
          frequency: "one-time",
          // Missing yearsInterval
        },
      ];

      const service = new CalculationService(
        expensesWithoutInterval,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      // Should handle gracefully and not include invalid one-time expenses
      expect(result.oneTimeOccurrences).toHaveLength(0);
      expect(result.aggregatedResults.breakdown.oneTime).toBe(0);
    });

    it("should handle very short retirement duration", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        1, // Only 1 year retirement
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      expect(result.yearlyBreakdowns).toHaveLength(1);
      expect(result.yearlyBreakdowns[0].year).toBe(1);
      expect(result.yearlyBreakdowns[0].age).toBe(defaultParams.retirementAge);
    });
  });

  describe("Utility Functions", () => {
    it("should calculateCosts function work correctly", () => {
      const result = calculateCosts(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      expect(result).toHaveProperty("currentMonthlyCost");
      expect(result).toHaveProperty("totalRetirementCost");
      expect(result).toHaveProperty("breakdown");
      expect(result).toHaveProperty("detailedBreakdown");
      expect(result.currentMonthlyCost).toBe(10000);
      expect(result.totalRetirementCost).toBeGreaterThan(0);
    });

    it("should calculateYearlyBreakdowns function work correctly", () => {
      const result = calculateYearlyBreakdowns(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      expect(result).toHaveLength(defaultParams.retirementDuration);
      expect(result[0]).toHaveProperty("year");
      expect(result[0]).toHaveProperty("age");
      expect(result[0]).toHaveProperty("totalYearlyExpenses");
      expect(result[0].year).toBe(1);
      expect(result[0].age).toBe(defaultParams.retirementAge);
    });

    it("should use default INFLATION_RATE when not provided", () => {
      const resultWithDefault = calculateCosts(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge
      );

      // Since the service expects percentage but INFLATION_RATE is decimal,
      // we need to test that they produce the same result
      expect(resultWithDefault.totalRetirementCost).toBeGreaterThan(0);
      expect(resultWithDefault.currentMonthlyCost).toBe(10000);
    });
  });

  describe("Inflation Calculations", () => {
    it("should calculate inflation multiplier correctly", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        6, // 6% inflation
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      // After 30 years (retirement age - current age), with 6% inflation
      // Multiplier should be approximately (1.06)^30 ≈ 5.74

      // Check that adjusted costs are significantly higher than current costs
      expect(result.costResults.adjustedMonthlyCost).toBeGreaterThan(
        result.costResults.currentMonthlyCost * 5
      );
    });

    it("should handle high inflation rates", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        20, // 20% inflation
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      // With high inflation, adjusted costs should be much higher
      expect(result.costResults.adjustedMonthlyCost).toBeGreaterThan(
        result.costResults.currentMonthlyCost * 100
      );
    });

    it("should show yearly cost increases in relation to inflation after retirement", () => {
      // Create a service with predictable expenses and inflation rate
      const testExpenses: ExpenseCategory[] = [
        {
          id: "1",
          name: "Monthly Living",
          iconName: "Home",
          monthlyAmount: 10000, // ₹10,000 per month
          frequency: "monthly",
        },
        {
          id: "2",
          name: "Annual Vacation",
          iconName: "Plane",
          monthlyAmount: 60000, // ₹60,000 per year
          frequency: "yearly",
        },
      ];

      const service = new CalculationService(
        testExpenses,
        35, // retirement age
        10, // retirement duration (10 years)
        30, // current age
        8, // 8% inflation rate
        12 // annual return
      );

      const result = service.calculateAll();
      const { yearlyBreakdowns } = result;

      // Verify we have the expected number of years
      expect(yearlyBreakdowns).toHaveLength(10);

      // Check that costs increase each year due to inflation during retirement
      for (let i = 1; i < yearlyBreakdowns.length; i++) {
        const currentYear = yearlyBreakdowns[i];
        const previousYear = yearlyBreakdowns[i - 1];

        // Total yearly expenses should increase each year
        expect(currentYear.totalYearlyExpenses).toBeGreaterThan(
          previousYear.totalYearlyExpenses
        );

        // Monthly expenses should increase each year
        expect(currentYear.monthlyExpenses).toBeGreaterThan(
          previousYear.monthlyExpenses
        );

        // Yearly expenses should increase each year
        expect(currentYear.yearlyExpenses).toBeGreaterThan(
          previousYear.yearlyExpenses
        );

        // Calculate expected inflation increase (should be approximately 8% per year)
        const expectedIncrease = previousYear.totalYearlyExpenses * 0.08;
        const actualIncrease =
          currentYear.totalYearlyExpenses - previousYear.totalYearlyExpenses;

        // Allow for some tolerance due to rounding
        expect(actualIncrease).toBeCloseTo(expectedIncrease, -2); // Within ₹100
      }

      // Verify the inflation progression matches expected compound growth
      const firstYear = yearlyBreakdowns[0];
      const fifthYear = yearlyBreakdowns[4]; // 5th year of retirement
      const lastYear = yearlyBreakdowns[9]; // 10th year of retirement

      // After 4 years of 8% inflation: cost should be approximately 1.08^4 ≈ 1.36 times higher
      const expectedFifthYearMultiplier = Math.pow(1.08, 4);
      console.log("First Year: ", firstYear.totalYearlyExpenses);
      console.log("Fifth Year: ", fifthYear.totalYearlyExpenses);
      expect(fifthYear.totalYearlyExpenses).toBeCloseTo(
        firstYear.totalYearlyExpenses * expectedFifthYearMultiplier,
        -3 // Within ₹1,000
      );

      // After 9 years of 8% inflation: cost should be approximately 1.08^9 ≈ 2.00 times higher
      const expectedLastYearMultiplier = Math.pow(1.08, 9);
      expect(lastYear.totalYearlyExpenses).toBeCloseTo(
        firstYear.totalYearlyExpenses * expectedLastYearMultiplier,
        -3 // Within ₹1,000
      );

      // Verify that the age progression is correct
      expect(firstYear.age).toBe(35);
      expect(lastYear.age).toBe(44);
      expect(firstYear.year).toBe(1);
      expect(lastYear.year).toBe(10);
    });
  });
});
