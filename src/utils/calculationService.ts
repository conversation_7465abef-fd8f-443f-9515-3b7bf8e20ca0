import { ExpenseCategory, CostResults } from "@/types/costCalculation";
import { INFLATION_RATE } from "@/constants/finance";

// Interfaces
export interface YearlyExpenseBreakdown {
  year: number;
  age: number;
  monthlyExpenses: number;
  yearlyExpenses: number;
  oneTimeExpenses: number;
  totalYearlyExpenses: number;
  expensesByCategory: {
    monthly: Array<{ name: string; amount: number }>;
    yearly: Array<{ name: string; amount: number }>;
    oneTime: Array<{ name: string; amount: number; occurrence: number }>;
  };
}

export interface AggregatedResults {
  totalRetirementCost: number;
  breakdown: { monthly: number; yearly: number; oneTime: number };
  averageAnnualCost: number;
  currentAnnualCost: number;
}

export interface ProjectionPoint {
  age: number;
  year: number;
  portfolioValue: number;
  realValue: number;
  cumulativeContributions: number;
  annualCost?: number;
  oneTimeCosts?: number;
  consolidatedCosts?: number;
  incomeFromInterest?: number;
  netPosition?: number;
  isRetirement?: boolean;
}

export interface OneTimeOccurrence {
  age: number;
  year: number;
  expenseName: string;
  amount: number;
  totalAmount: number;
}

export interface ProjectionInputData {
  yearlyProjections: ProjectionPoint[];
  finalValue: number;
  totalContributions: number;
  monthlyRetirementIncome: number;
  realMonthlyIncome: number;
  currentAge: number;
}

export class CalculationService {
  private expenses: ExpenseCategory[];
  private retirementAge: number;
  private retirementDuration: number;
  private currentAge: number;
  private inflationRate: number;
  private annualReturn: number;

  constructor(
    expenses: ExpenseCategory[],
    retirementAge: number,
    retirementDuration: number,
    currentAge: number,
    inflationRate: number = INFLATION_RATE,
    annualReturn: number = 12
  ) {
    this.expenses = expenses;
    this.retirementAge = retirementAge;
    this.retirementDuration = retirementDuration;
    this.currentAge = currentAge;
    this.inflationRate = inflationRate;
    this.annualReturn = annualReturn;
  }

  private getInflationMultiplier(years: number): number {
    return Math.pow(1 + this.inflationRate / 100, years);
  }

  calculateAll() {
    const yearsToRetirement = this.retirementAge - this.currentAge;
    const currentYear = new Date().getFullYear();
    const inflationMultiplierAtRetirement =
      this.getInflationMultiplier(yearsToRetirement);

    // Initialize aggregation variables
    const yearlyBreakdowns: YearlyExpenseBreakdown[] = [];
    const oneTimeOccurrences: OneTimeOccurrence[] = [];
    let totalMonthlyExpenses = 0;
    let totalYearlyExpenses = 0;
    let totalOneTimeExpenses = 0;

    // Calculate current costs (before inflation)
    const currentCosts = this.expenses.reduce(
      (acc, expense) => {
        if (expense.frequency === "monthly") {
          acc.monthly += expense.monthlyAmount;
        } else if (expense.frequency === "yearly") {
          acc.yearly += expense.monthlyAmount;
        } else if (expense.frequency === "one-time" && expense.yearsInterval) {
          const occurrences = Math.floor(
            this.retirementDuration / expense.yearsInterval
          );
          acc.oneTime += expense.monthlyAmount * occurrences;
        }
        return acc;
      },
      { monthly: 0, yearly: 0, oneTime: 0 }
    );

    // Single loop for all calculations
    for (let year = 0; year < this.retirementDuration; year++) {
      const currentAge = this.retirementAge + year;
      const yearInflationMultiplier = this.getInflationMultiplier(year);
      let currentMonthlyCost = 0;
      let currentYearlyCost = 0;
      let currentOneTimeCost = 0;

      const monthlyExpenses: Array<{ name: string; amount: number }> = [];
      const yearlyExpenses: Array<{ name: string; amount: number }> = [];
      const oneTimeExpenses: Array<{
        name: string;
        amount: number;
        occurrence: number;
      }> = [];

      // Process each expense for this year
      this.expenses.forEach((expense) => {
        if (expense.frequency === "monthly") {
          const adjustedAmount =
            expense.monthlyAmount *
            inflationMultiplierAtRetirement *
            yearInflationMultiplier;
          currentMonthlyCost += adjustedAmount;
          monthlyExpenses.push({ name: expense.name, amount: adjustedAmount });
        } else if (expense.frequency === "yearly") {
          const adjustedAmount =
            expense.monthlyAmount *
            inflationMultiplierAtRetirement *
            yearInflationMultiplier;
          currentYearlyCost += adjustedAmount;
          yearlyExpenses.push({ name: expense.name, amount: adjustedAmount });
        } else if (expense.frequency === "one-time" && expense.yearsInterval) {
          const occurrenceYear = year + 1;
          if (occurrenceYear % expense.yearsInterval === 0) {
            const totalInflationMultiplier = this.getInflationMultiplier(
              yearsToRetirement + year
            );
            const adjustedAmount =
              expense.monthlyAmount * totalInflationMultiplier;

            currentOneTimeCost += adjustedAmount;
            const occurrence = Math.floor(
              occurrenceYear / expense.yearsInterval
            );
            oneTimeExpenses.push({
              name: expense.name,
              amount: adjustedAmount,
              occurrence,
            });

            // Add to one-time occurrences list
            oneTimeOccurrences.push({
              age: currentAge,
              year: currentYear + yearsToRetirement + year,
              expenseName: expense.name,
              amount: adjustedAmount,
              totalAmount: adjustedAmount,
            });
          }
        }
      });

      // Calculate totals for this year
      const yearlyMonthlyExpenses = currentMonthlyCost * 12;
      const totalYearlyExpensesForThisYear =
        yearlyMonthlyExpenses + currentYearlyCost + currentOneTimeCost;

      // Add to yearly breakdown
      yearlyBreakdowns.push({
        year: year + 1,
        age: currentAge,
        monthlyExpenses: yearlyMonthlyExpenses,
        yearlyExpenses: currentYearlyCost,
        oneTimeExpenses: currentOneTimeCost,
        totalYearlyExpenses: totalYearlyExpensesForThisYear,
        expensesByCategory: {
          monthly: monthlyExpenses,
          yearly: yearlyExpenses,
          oneTime: oneTimeExpenses,
        },
      });

      // Aggregate totals
      totalMonthlyExpenses += yearlyMonthlyExpenses;
      totalYearlyExpenses += currentYearlyCost;
      totalOneTimeExpenses += currentOneTimeCost;
    }

    // Calculate aggregated results
    const breakdown = {
      monthly: totalMonthlyExpenses,
      yearly: totalYearlyExpenses,
      oneTime: totalOneTimeExpenses,
    };

    const totalRetirementCost =
      breakdown.monthly + breakdown.yearly + breakdown.oneTime;
    const averageAnnualCost = totalRetirementCost / this.retirementDuration;
    const currentAnnualCost =
      currentCosts.monthly * 12 +
      currentCosts.yearly +
      currentCosts.oneTime / this.retirementDuration;

    const aggregatedResults: AggregatedResults = {
      totalRetirementCost,
      breakdown,
      averageAnnualCost,
      currentAnnualCost,
    };

    // Generate cost results
    const adjustedMonthlyCostAtRetirement =
      currentCosts.monthly * inflationMultiplierAtRetirement;
    const adjustedYearlyCostAtRetirement =
      currentCosts.yearly * inflationMultiplierAtRetirement;

    const yearlyExpensesByCategory = this.expenses
      .filter((expense) => expense.frequency === "yearly")
      .map((expense) => ({
        name: expense.name,
        currentAmount: expense.monthlyAmount,
        adjustedAmount: expense.monthlyAmount * inflationMultiplierAtRetirement,
        frequency: "yearly" as const,
      }));

    const oneTimeExpensesByCategory = this.expenses
      .filter(
        (expense) => expense.frequency === "one-time" && expense.yearsInterval
      )
      .map((expense) => {
        let occurrences = 0;
        let totalCost = 0;

        for (
          let year = expense.yearsInterval!;
          year <= this.retirementDuration;
          year += expense.yearsInterval!
        ) {
          occurrences++;
          const yearInflationMultiplier = this.getInflationMultiplier(
            yearsToRetirement + year
          );
          totalCost += expense.monthlyAmount * yearInflationMultiplier;
        }

        return {
          name: expense.name,
          costPerOccurrence: expense.monthlyAmount,
          yearsInterval: expense.yearsInterval!,
          totalOccurrences: occurrences,
          totalCost,
          frequency: "one-time" as const,
        };
      });

    const costResults: CostResults = {
      currentMonthlyCost: currentCosts.monthly,
      currentAnnualCost: aggregatedResults.currentAnnualCost,
      adjustedMonthlyCost: adjustedMonthlyCostAtRetirement,
      adjustedAnnualCost: aggregatedResults.averageAnnualCost,
      totalRetirementCost: aggregatedResults.totalRetirementCost,
      oneTimeCosts: aggregatedResults.breakdown.oneTime,
      breakdown: aggregatedResults.breakdown,
      detailedBreakdown: {
        currentYearlyCost: currentCosts.yearly,
        adjustedYearlyCost: adjustedYearlyCostAtRetirement,
        yearlyExpensesByCategory,
        oneTimeExpensesByCategory,
      },
    };

    // Sort one-time occurrences by age
    oneTimeOccurrences.sort((a, b) => a.age - b.age);

    return {
      yearlyBreakdowns,
      aggregatedResults,
      costResults,
      oneTimeOccurrences,
    };
  }

  generateEnhancedProjections(
    projectionData: ProjectionInputData,
    oneTimeOccurrences: OneTimeOccurrence[],
    adjustedAnnualCost: number
  ): ProjectionPoint[] {
    const enhancedData: ProjectionPoint[] = [
      ...projectionData.yearlyProjections,
    ];
    let remainingPortfolio = projectionData.finalValue;

    for (let i = 1; i <= this.retirementDuration; i++) {
      const age = this.retirementAge + i;
      const year = enhancedData[enhancedData.length - 1].year + 1;

      const oneTimeCostsThisYear = oneTimeOccurrences
        .filter((occurrence) => occurrence.age === age)
        .reduce((sum, occurrence) => sum + occurrence.amount, 0);

      // Apply inflation during retirement years
      // The adjustedAnnualCost is the cost at the start of retirement
      // We need to apply additional inflation for each year during retirement
      const yearInflationMultiplier = this.getInflationMultiplier(i - 1);
      const inflatedAnnualCost = adjustedAnnualCost * yearInflationMultiplier;

      const consolidatedCosts = inflatedAnnualCost + oneTimeCostsThisYear;
      const incomeFromInterest = remainingPortfolio * (this.annualReturn / 100);
      const netPosition = incomeFromInterest - consolidatedCosts;

      remainingPortfolio = Math.max(
        0,
        remainingPortfolio * (1 + this.annualReturn / 100) -
          inflatedAnnualCost -
          oneTimeCostsThisYear
      );
      const realValue = remainingPortfolio / this.getInflationMultiplier(i);

      enhancedData.push({
        age,
        year,
        portfolioValue: remainingPortfolio,
        realValue,
        cumulativeContributions: projectionData.totalContributions,
        annualCost: inflatedAnnualCost,
        oneTimeCosts:
          oneTimeCostsThisYear > 0 ? oneTimeCostsThisYear : undefined,
        consolidatedCosts,
        incomeFromInterest,
        netPosition,
        isRetirement: true,
      });
    }

    return enhancedData;
  }
}

// Utility functions for backward compatibility
export const calculateCosts = (
  expenses: ExpenseCategory[],
  retirementAge: number,
  retirementDuration: number,
  currentAge: number,
  inflationRate: number = INFLATION_RATE,
  annualReturn: number = 12
): CostResults => {
  const service = new CalculationService(
    expenses,
    retirementAge,
    retirementDuration,
    currentAge,
    inflationRate,
    annualReturn
  );
  return service.calculateAll().costResults;
};

export const calculateYearlyBreakdowns = (
  expenses: ExpenseCategory[],
  retirementAge: number,
  retirementDuration: number,
  currentAge: number,
  inflationRate: number = INFLATION_RATE,
  annualReturn: number = 12
): YearlyExpenseBreakdown[] => {
  const service = new CalculationService(
    expenses,
    retirementAge,
    retirementDuration,
    currentAge,
    inflationRate,
    annualReturn
  );
  return service.calculateAll().yearlyBreakdowns;
};
